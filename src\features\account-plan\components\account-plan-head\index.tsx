"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useParams } from "next/navigation";
import { IconChevronDown } from "@tabler/icons-react";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import { cn, getFullName } from "@/lib/utils";
import { QUERY_KEYS } from "@/constants/query-keys";
import { DownloadAnalysisButton } from "./download-analysis";
import { currencies } from "@/constants/currencies";
import { Option } from "@/components/ui/autocomplete";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { isRequestError } from "@/lib/api-client";

import { useAccountPlanGroups } from "../../api/account-plan-group/get-account-plan-group";
import { useUpdateAccountPlanGroups } from "../../api/account-plan-group/update-account-plan-group";
import { useAccountPlan } from "../../api/get-account-plan";
import { useUpdateAccountPlan } from "../../api/update-account-plan";
import {
  AccountPlanData,
  AccountPlanGroupsData,
  AccountPlanGroupsPayload,
  AccountPlanPayload,
} from "../../types";
import { useIndustryList } from "../../api/get-industry-list";
import {
  AccountMetadataAutocomplete,
  AccountMetadataInput,
  AccountMetadataDateInput,
} from "../account-metadata-fields";

const nextReviewOptions = [3, 6, 9, 12, 18, 24, 36, 72];

export const AccountPlanHead = ({
  accountPlan,
  accountPlanGroups,
}: {
  accountPlan?: AccountPlanData;
  accountPlanGroups?: AccountPlanGroupsData;
}) => {
  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const queryClient = useQueryClient();

  const updateAccountPlan = useUpdateAccountPlan({});
  const updateAccountPlanGroup = useUpdateAccountPlanGroups({});
  const { industryOptions } = useIndustryList({});

  const [industry, setIndustry] = useState<Option | undefined>(undefined);
  const [currency, setCurrency] = useState("");
  const [nextReview, setNextReview] = useState<number>();
  const [isCollapsibleOpen, setIsCollapsibleOpen] = useState(false);

  const currencyOptions = useMemo(() => {
    return currencies.map((v) => ({
      ...v,
      label: `${v.code} ${v.symbolNative} - ${v.name}`,
      value: `${v.code} ${v.symbolNative} - ${v.name}`,
    }));
  }, []);

  useEffect(() => {
    if (accountPlan) {
      const { next_review_date } = accountPlan;

      if (next_review_date) {
        setNextReview(next_review_date);
      }
    }
  }, [accountPlan]);

  useEffect(() => {
    if (!accountPlanGroups) return;

    setCurrency(
      currencyOptions.find((v) => accountPlanGroups.currency === v.name)
        ?.label ?? ""
    );
    setIndustry({
      label: accountPlanGroups?.industry?.name ?? "",
      value: accountPlanGroups?.industry?.name ?? "",
    });
  }, [accountPlanGroups, currencyOptions]);

  const onUpdate = async (data: AccountPlanPayload) => {
    try {
      await updateAccountPlan.mutateAsync({
        accountId,
        data,
      });

      toast("Your changes has been saved");
    } catch (_) {
      toast.error("An error occured while updating the data");
    }
  };

  const onUpdateGroup = async (
    data: AccountPlanGroupsPayload,
    invalidate?: boolean,
    onError?: () => void
  ) => {
    if (!accountPlan?.account_plan_group?.id) return;

    try {
      await updateAccountPlanGroup.mutateAsync({
        accountGroupId: accountPlan?.account_plan_group?.id,
        data,
      });

      toast("Your changes has been saved");

      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.ACCOUNT_PLANS, accountId],
        });
      }
    } catch (e) {
      if (isRequestError(e)) {
        const errorMessage = e.response?.data.errors[0].message ?? "";
        toast.error(errorMessage);
      } else {
        toast.error("An error occured while updating the data");
      }

      onError?.();
    }
  };

  return (
    <>
      <div className="flex h-[5vh] w-full items-center justify-center gap-4 px-4">
        <IconChevronDown
          className={cn(
            "cursor-pointer text-primary-500 transition-all",
            isCollapsibleOpen && "rotate-180"
          )}
          onClick={() => setIsCollapsibleOpen((prev) => !prev)}
        />
      </div>

      {isCollapsibleOpen && (
        <div className="mb-[15px] flex h-[5vh] w-full items-center justify-between gap-4 px-4">
          <div className="w-[30%]">
            <div className="text-3xl font-medium">
              Pay Nexus Financial Solution
            </div>
          </div>
          <div className="flex w-[30%] justify-end">
            <DownloadAnalysisButton
              accountPlan={accountPlan}
              accountPlanGroups={accountPlanGroups}
            />
          </div>
        </div>
      )}

      <Collapsible open={isCollapsibleOpen}>
        <CollapsibleContent className="mb-8">
          <section className="flex flex-col gap-4 text-primary-500">
            <div className="flex items-center justify-between">
              <div className="w-[18%]">
                <AccountMetadataInput
                  title="Account ID"
                  tooltip="The unique internal refence number for this account plan."
                  defaultValue={accountPlanGroups?.account_plan_unique_id ?? ""}
                  onSave={(account_plan_unique_id, { reset }) => {
                    if (
                      account_plan_unique_id ===
                      accountPlanGroups?.account_plan_unique_id
                    )
                      return;

                    onUpdateGroup({ account_plan_unique_id }, false, () =>
                      reset()
                    );
                  }}
                />
              </div>

              <div className="w-[18%]">
                <AccountMetadataInput
                  title="Version Name"
                  defaultValue={accountPlan?.version ?? ""}
                  onSave={(version) => onUpdate({ version })}
                  withWarning={false}
                />
              </div>

              <div className="w-[18%]">
                <AccountMetadataInput
                  required
                  title="Account Addressable Area"
                  tooltip="This is the manageable portion of the company you are managing which could be defined by function, geography, or product/service. It normally reflects how they buy for you."
                  placeholder="Enter account addressable area"
                  defaultValue={
                    accountPlanGroups?.account_addressable_area ?? ""
                  }
                  onSave={(account_addressable_area) =>
                    onUpdateGroup({ account_addressable_area })
                  }
                />
              </div>

              <div className="w-[18%]">
                <AccountMetadataInput
                  title="Account Owner"
                  defaultValue={getFullName(
                    accountPlan?.owner_user?.first_name,
                    accountPlan?.owner_user?.last_name
                  )}
                  disabled
                />
              </div>

              <div className="w-[18%]">
                <AccountMetadataAutocomplete
                  required
                  title="Currency"
                  options={currencyOptions}
                  placeholder="Search Currency"
                  value={{ label: currency, value: currency }}
                  onValueChange={(currencyData) => {
                    const matchedCurrency = currencyOptions.find(
                      (v) => v.label === currencyData.label
                    );

                    setCurrency(matchedCurrency?.label ?? "");

                    onUpdateGroup(
                      { currency: matchedCurrency?.name ?? null },
                      true
                    );
                  }}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="w-[18%]">
                {" "}
                <AccountMetadataAutocomplete
                  required
                  title="Industry"
                  placeholder="Search Industry Name"
                  options={industryOptions}
                  value={industry}
                  onValueChange={(industryData) => {
                    setIndustry(industryData);

                    onUpdateGroup({
                      industry_id: !!industryData.label
                        ? industryOptions.find(
                            (v) => v.label === industryData.label
                          )?.id
                        : null,
                    });
                  }}
                />
              </div>

              <div className="w-[18%]">
                <AccountMetadataInput
                  required
                  title="Company"
                  placeholder="Enter company name"
                  defaultValue={accountPlanGroups?.company ?? ""}
                  onSave={(company) => {
                    onUpdateGroup({ company });
                  }}
                />
              </div>

              <div className="w-[18%]">
                <AccountMetadataInput
                  required
                  title="Location"
                  placeholder="Enter location name"
                  defaultValue={accountPlanGroups?.location ?? ""}
                  onSave={(location) => onUpdateGroup({ location })}
                />
              </div>

              <div className="w-[18%]">
                <div className="review-date-wrapper">
                  <AccountMetadataDateInput
                    title="Review Date"
                    defaultValue={accountPlan?.review_date}
                    onSave={(review_date) => onUpdate({ review_date })}
                    withWarning={false}
                  />
                </div>
                <style
                  dangerouslySetInnerHTML={{
                    __html: `
                    .review-date-wrapper button {
                      background-color: #fff !important;
                    }
                  `,
                  }}
                />
              </div>

              <div className="!mt-[-10px] w-[18%]">
                <div className="select-interval-wrapper">
                  <Select
                    value={nextReview?.toString()}
                    onValueChange={(val) => {
                      const next_review_date = parseInt(val);

                      setNextReview(next_review_date);
                      onUpdate({ next_review_date });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Interval" />
                    </SelectTrigger>
                    <SelectContent>
                      {nextReviewOptions.map((option, idx) => (
                        <SelectItem key={idx} value={option.toString()}>
                          {option} months
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <style
                  dangerouslySetInnerHTML={{
                    __html: `
                    .select-interval-wrapper button {
                      background-color: #fff !important;
                    }
                  `,
                  }}
                />
              </div>
            </div>
          </section>
        </CollapsibleContent>
      </Collapsible>
    </>
  );
};
